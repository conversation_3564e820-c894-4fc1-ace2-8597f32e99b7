# ContiNew Admin

<div align="center">

![ContiNew Admin](https://img.shields.io/badge/ContiNew%20Admin-v3.7.0-brightgreen)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.x-brightgreen)
![Java](https://img.shields.io/badge/Java-17+-orange)
![Vue](https://img.shields.io/badge/Vue-3.x-brightgreen)
![License](https://img.shields.io/badge/License-Apache%202.0-blue)

**持续迭代优化的前后端分离中后台管理系统框架**

开箱即用，重视每一处代码规范，重视每一种解决方案细节，持续提供舒适的前、后端开发体验。

[在线文档](https://continew.top) | [演示地址](https://continew.top/admin/guide/demo.html) | [更新日志](CHANGELOG.md)

</div>

## 📋 项目简介

ContiNew Admin（Continue New Admin）是一个基于 Spring Boot 3 + Vue 3 的前后端分离中后台管理系统框架。项目采用现代化的技术栈，提供完整的权限管理、用户管理、部门管理等基础功能，同时集成了代码生成器、任务调度、系统监控等实用工具。

### ✨ 核心特性

- 🚀 **现代化技术栈**：Spring Boot 3 + Java 17 + Vue 3 + TypeScript + Vite
- 🔐 **完善的权限系统**：基于 Sa-Token 的 RBAC 权限模型，支持多租户
- 🎨 **优雅的前端界面**：基于 Arco Design 的现代化 UI 设计
- 🛠️ **代码生成器**：支持前后端代码一键生成，提高开发效率
- 📊 **系统监控**：完整的日志管理、在线用户管理、系统监控
- 🔧 **任务调度**：集成 Snail Job 分布式任务调度系统
- 🌐 **多端支持**：支持 PC 端、移动端等多端认证管理
- 📦 **开箱即用**：提供完整的开发环境配置和部署方案

## 🏗️ 项目架构

```mermaid
graph TB
    subgraph "前端层"
        A[Vue3 + Arco Design + TypeScript + Vite]
        A1[用户界面]
        A2[路由管理]
        A3[状态管理]
        A4[组件库]
    end
    
    subgraph "API网关层"
        B[continew-webapi]
        B1[Controller层]
        B2[认证拦截器]
        B3[全局异常处理]
        B4[跨域配置]
    end
    
    subgraph "业务服务层"
        C[continew-module-system]
        C1[用户管理]
        C2[角色管理]
        C3[部门管理]
        C4[菜单管理]
        C5[权限管理]
        
        D[continew-plugin]
        D1[代码生成器]
        D2[任务调度]
        D3[开放平台]
    end
    
    subgraph "公共组件层"
        E[continew-common]
        E1[工具类]
        E2[常量定义]
        E3[枚举类型]
        E4[基础配置]
    end
    
    subgraph "安全认证层"
        G[Sa-Token]
        G1[JWT认证]
        G2[权限验证]
        G3[会话管理]
        G4[多端登录]
    end
    
    subgraph "数据访问层"
        H[MyBatis Plus]
        H1[实体映射]
        H2[CRUD操作]
        H3[分页查询]
        H4[数据权限]
    end
    
    subgraph "缓存层"
        I[Redis + JetCache]
        I1[会话缓存]
        I2[权限缓存]
        I3[业务缓存]
        I4[分布式锁]
    end
    
    subgraph "数据库层"
        J[(MySQL/PostgreSQL)]
        J1[用户数据]
        J2[权限数据]
        J3[业务数据]
        J4[日志数据]
    end
    
    A --> B
    B --> C
    B --> D
    C --> E
    D --> E
    B --> G
    C --> H
    D --> H
    H --> J
    G --> I
    C --> I
```

## 🛠️ 技术栈

### 后端技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 3.x | 基础框架 |
| Java | 17+ | 开发语言 |
| Sa-Token | 最新版 | 权限认证框架 |
| MyBatis Plus | 最新版 | ORM 框架 |
| Redis | 5.0+ | 缓存数据库 |
| MySQL | 8.0+ | 主数据库 |
| PostgreSQL | 12+ | 可选数据库 |
| JetCache | 最新版 | 多级缓存框架 |
| Redisson | 最新版 | Redis 客户端 |
| Hutool | 最新版 | Java 工具类库 |
| EasyExcel | 最新版 | Excel 处理 |
| Liquibase | 最新版 | 数据库版本管理 |
| JustAuth | 最新版 | 第三方登录 |
| Crane4j | 最新版 | 数据填充框架 |

### 前端技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Vue | 3.x | 前端框架 |
| TypeScript | 最新版 | 开发语言 |
| Vite | 最新版 | 构建工具 |
| Arco Design | 最新版 | UI 组件库 |
| Vue Router | 4.x | 路由管理 |
| Pinia | 最新版 | 状态管理 |
| Axios | 最新版 | HTTP 客户端 |

## 📁 项目结构

```
continew-admin
├─ continew-webapi（API 及打包部署模块）
│  ├─ src/main/java/top/continew/admin
│  │  ├─ config（配置类）
│  │  ├─ controller（控制器层）
│  │  │  ├─ auth（认证相关 API）
│  │  │  ├─ common（通用 API）
│  │  │  ├─ monitor（监控相关 API）
│  │  │  ├─ system（系统管理 API）
│  │  │  └─ tool（工具相关 API）
│  │  └─ ContiNewAdminApplication.java（启动类）
│  └─ src/main/resources
│     ├─ config（配置文件目录）
│     │  ├─ application.yml（通用配置）
│     │  ├─ application-dev.yml（开发环境配置）
│     │  └─ application-prod.yml（生产环境配置）
│     ├─ db/changelog（数据库脚本）
│     └─ templates（模板文件）
├─ continew-module-system（系统管理模块）
│  └─ src/main/java/top/continew/admin
│     ├─ auth（认证相关业务）
│     └─ system（系统管理业务）
│        ├─ controller（控制器）
│        ├─ service（业务层）
│        ├─ mapper（数据访问层）
│        └─ model（数据模型）
├─ continew-plugin（插件模块）
│  ├─ continew-plugin-generator（代码生成器）
│  ├─ continew-plugin-open（开放平台）
│  └─ continew-plugin-schedule（任务调度）
├─ continew-common（公共模块）
│  └─ src/main/java/top/continew/admin/common
│     ├─ config（公共配置）
│     ├─ constant（常量定义）
│     ├─ enums（枚举类型）
│     └─ util（工具类）
├─ continew-extension（扩展模块）
│  └─ continew-extension-schedule-server（任务调度服务器）
├─ docker（Docker 部署配置）
└─ pom.xml（Maven 主配置文件）
```

## 🚀 快速开始

### 环境要求

- **JDK**: 17+
- **Maven**: 3.6+
- **Node.js**: 16+
- **MySQL**: 8.0+ 或 **PostgreSQL**: 12+
- **Redis**: 5.0+

### 后端启动

1. **克隆项目**
```bash
git clone https://github.com/continew-org/continew-admin.git
cd continew-admin
```

2. **创建数据库**
```sql
CREATE DATABASE continew_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **修改配置**
编辑 `continew-webapi/src/main/resources/config/application-dev.yml`：
```yaml
spring:
  datasource:
    url: *************************************************************************************************************************************************************************************************************************************
    username: root
    password: 你的数据库密码
```

4. **启动 Redis**
```bash
redis-server
```

5. **编译运行**
```bash
# 编译项目
mvn clean compile

# 启动项目
mvn spring-boot:run -pl continew-webapi
```

访问 http://localhost:8000 查看 API 文档。

### 前端启动

> 注意：当前仓库仅包含后端代码，前端代码请访问：[continew-admin-ui](https://github.com/continew-org/continew-admin-ui)

```bash
# 克隆前端项目
git clone https://github.com/continew-org/continew-admin-ui.git
cd continew-admin-ui

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

访问 http://localhost:5173 进入系统。

默认账号密码：
- 超级管理员：`admin` / `admin123`
- 普通用户：`user` / `user123`

## 📚 核心功能

### 系统管理
- **用户管理**：用户的维护、角色分配、数据权限分配等
- **角色管理**：角色的维护、权限分配、数据权限分配等  
- **部门管理**：部门的维护、树形结构展示
- **菜单管理**：菜单的维护、权限标识、菜单类型等
- **字典管理**：系统字典及字典项的维护
- **系统配置**：系统的基础配置信息维护
- **存储配置**：文件存储配置，支持本地存储、兼容 S3 协议对象存储
- **客户端配置**：多端认证管理，可设置不同的 token 有效期

### 系统监控
- **在线用户**：管理当前登录用户，可一键踢除下线
- **日志管理**：系统登录日志、操作日志管理
- **短信日志**：系统短信发送日志管理

### 任务管理
- **任务管理**：系统定时任务管理，支持 Cron 和固定频率
- **任务日志**：定时任务执行日志，支持查看详细输出日志

### 开发工具
- **代码生成**：根据数据库表自动生成前后端 CRUD 代码
- **应用管理**：第三方系统应用 AK、SK 管理

## 🔧 开发指南

### 代码规范

项目严格遵循阿里巴巴 Java 开发手册，使用 Spotless 进行代码格式化。

### 数据库设计

- 使用 Liquibase 进行数据库版本管理
- 支持 MySQL 和 PostgreSQL 双数据库
- 采用逻辑删除和乐观锁机制

### 权限设计

- 基于 RBAC 模型的权限管理
- 支持菜单权限、按钮权限、数据权限
- 支持多租户隔离

### API 设计

- 遵循 RESTful API 设计规范
- 统一的响应格式和异常处理
- 完整的 Swagger API 文档

## 🐳 部署指南

### Docker 部署

1. **构建镜像**
```bash
# 构建后端镜像
docker build -t continew-admin:latest .
```

2. **使用 Docker Compose**
```bash
cd docker
docker-compose up -d
```

### 传统部署

1. **打包项目**
```bash
mvn clean package -Dmaven.test.skip=true
```

2. **部署到服务器**
```bash
# 上传 target/continew-admin.jar 到服务器
java -jar continew-admin.jar --spring.profiles.active=prod
```

## 🤝 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug 修复
- ✨ 新功能开发  
- 📝 文档完善
- 🎨 UI/UX 改进
- 🧪 测试用例

### 贡献流程

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范

- 遵循项目现有的代码风格
- 添加必要的测试用例
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目基于 [Apache License 2.0](LICENSE) 开源协议，请自由地享受和参与开源。

## 🙏 致谢

感谢以下优秀的开源项目：

- [Spring Boot](https://spring.io/projects/spring-boot)
- [Sa-Token](https://sa-token.cc/)
- [MyBatis Plus](https://baomidou.com/)
- [Vue.js](https://vuejs.org/)
- [Arco Design](https://arco.design/)
- [Hutool](https://hutool.cn/)

## 📞 联系我们

- 📧 邮箱：<EMAIL>
- 🌐 官网：https://continew.top
- 📖 文档：https://continew.top
- 🎯 演示：https://continew.top/admin/guide/demo.html

---

<div align="center">

**如果这个项目对你有帮助，请给个 ⭐️ Star 支持一下！**

</div>
