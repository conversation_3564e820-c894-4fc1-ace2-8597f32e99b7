<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew</groupId>
        <artifactId>continew-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-common</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>公共模块（存放公共工具类，公共配置等）</description>

    <dependencies>
        <!-- CosId（通用、灵活、高性能的分布式 ID 生成器） -->
        <dependency>
            <groupId>me.ahoo.cosid</groupId>
            <artifactId>cosid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>me.ahoo.cosid</groupId>
            <artifactId>cosid-spring-redis</artifactId>
        </dependency>

        <!-- X File Storage（一行代码将文件存储到本地、FTP、SFTP、WebDAV、阿里云 OSS、华为云 OBS...等其它兼容 S3 协议的存储平台） -->
        <dependency>
            <groupId>org.dromara.x-file-storage</groupId>
            <artifactId>x-file-storage-spring</artifactId>
        </dependency>
        <!-- Amazon S3（Amazon Simple Storage Service，亚马逊简单存储服务，通用存储协议 S3，兼容主流云厂商对象存储） -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>

        <!-- FreeMarker（模板引擎） -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>

        <!-- MySQL Java 驱动 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- PostgreSQL Java 驱动 -->
        <!--<dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>-->

        <!-- ContiNew Starter 扩展模块 - CURD（增删改查） -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-extension-crud-mp</artifactId>
        </dependency>

        <!-- ContiNew Starter 认证模块 - SaToken -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-auth-satoken</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ContiNew Starter 认证模块 - JustAuth -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-auth-justauth</artifactId>
        </dependency>

        <!-- ContiNew Starter 缓存模块 - JetCache -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-cache-jetcache</artifactId>
        </dependency>

        <!-- ContiNew Starter 数据权限模块 - MyBatis Plus -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-extension-datapermission-mp</artifactId>
        </dependency>

        <!-- ContiNew Starter 消息模块 - WebSocket -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-messaging-websocket</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ContiNew Starter 消息模块 - 邮件 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-messaging-mail</artifactId>
        </dependency>

        <!-- ContiNew Starter 验证码模块 - 图形验证码 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-captcha-graphic</artifactId>
        </dependency>

        <!-- ContiNew Starter 验证码模块 - 行为验证码 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-captcha-behavior</artifactId>
        </dependency>

        <!-- ContiNew Starter 限流模块 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-ratelimiter</artifactId>
        </dependency>

        <!-- ContiNew Starter 安全模块 - 加密 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-security-crypto</artifactId>
        </dependency>

        <!-- ContiNew Starter 安全模块 - 脱敏 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-security-mask</artifactId>
        </dependency>

        <!-- ContiNew Starter 安全模块 - 密码编码器 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-security-password</artifactId>
        </dependency>

        <!-- ContiNew Starter JSON 模块 - Jackson -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-json-jackson</artifactId>
        </dependency>
    </dependencies>
</project>