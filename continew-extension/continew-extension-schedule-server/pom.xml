<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew</groupId>
        <artifactId>continew-extension</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-extension-schedule-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>任务调度服务端</description>

    <properties>
        <!-- SnailJob 服务端 -->
        <snail-job.version>1.4.0</snail-job.version>
    </properties>

    <dependencies>
        <!-- SnailJob（灵活，可靠和快速的分布式任务重试和分布式任务调度平台） -->
        <dependency>
            <groupId>com.aizuda</groupId>
            <artifactId>snail-job-server-starter</artifactId>
            <version>${snail-job.version}</version>
        </dependency>

        <!-- Liquibase（用于管理数据库版本，跟踪、管理和应用数据库变化） -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>