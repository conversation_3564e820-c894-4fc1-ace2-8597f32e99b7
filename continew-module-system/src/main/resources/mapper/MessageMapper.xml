<?xml version="1.0" encoding="UTF-8" ?> <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.system.mapper.MessageMapper">

    <select id="selectMessagePage" resultType="top.continew.admin.system.model.resp.message.MessageResp">
        SELECT
            t1.id,
            t1.title,
            t1.type,
            t1.path,
            t1.scope,
            t1.users,
            t1.create_time,
            t2.read_time IS NOT NULL AS isRead,
            t2.read_time AS readTime
        FROM sys_message AS t1
            LEFT JOIN sys_message_log AS t2 ON t2.message_id = t1.id
        <where>
            <if test="query.userId != null">
                (t1.scope = 1 OR (t1.scope = 2 AND JSON_EXTRACT(t1.users, "$[0]") = CAST(#{query.userId} AS CHAR)))
            </if>
            <if test="query.title != null and query.title != ''">
                AND t1.title LIKE CONCAT('%', #{query.title}, '%')
            </if>
            <if test="query.type != null and query.type != ''">
                AND t1.type = #{query.type}
            </if>
            <if test="query.isRead != null">
                AND t2.read_time IS <if test="query.isRead">NOT</if> NULL
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>

    <select id="selectMessageById" resultType="top.continew.admin.system.model.resp.message.MessageDetailResp">
        SELECT
            t1.id,
            t1.title,
            t1.content,
            t1.type,
            t1.path,
            t1.scope,
            t1.users,
            t1.create_time,
            t2.read_time IS NOT NULL AS isRead,
            t2.read_time AS readTime
        FROM sys_message AS t1
            LEFT JOIN sys_message_log AS t2 ON t2.message_id = t1.id
        WHERE t1.id = #{id}
    </select>

    <select id="selectUnreadListByUserId" resultType="top.continew.admin.system.model.entity.MessageDO">
        SELECT
            t1.*
        FROM sys_message AS t1
            LEFT JOIN sys_message_log AS t2 ON t2.message_id = t1.id
        WHERE (t1.scope = 1 OR (t1.scope = 2 AND JSON_CONTAINS(t1.users, CONCAT('"', #{userId}, '"'))))
        AND t2.read_time IS NULL
    </select>

    <select id="selectUnreadCountByUserIdAndType" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM sys_message AS t1
            LEFT JOIN sys_message_log AS t2 ON t2.message_id = t1.id
        WHERE (t1.scope = 1 OR (t1.scope = 2 AND JSON_CONTAINS(t1.users, CONCAT('"', #{userId}, '"'))))
        AND t2.read_time IS NULL
          <if test="type != null">
            AND t1.type = #{type}
          </if>
    </select>
</mapper>
